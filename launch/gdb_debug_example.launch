<launch>

  <arg name="rviz" default="true" />

  <node pkg="fast_lio" type="fastlio_mapping" name="laserMapping" output="screen" required="true" launch-prefix="gdb -ex run --args">
	<param name="imu_topic" type="string" value="/livox/imu" />
	<param name="map_file_path" type="string" value=" " />
	<param name="max_iteration" type="int" value="4" />
	<param name="dense_map_enable" type="bool" value="1" />
	<param name="fov_degree" type="double" value="75" />
	<param name="filter_size_corner" type="double" value="0.2" />
	<param name="filter_size_surf" type="double" value="0.2" />
	<param name="filter_size_map" type="double" value="0.5" />
	<param name="runtime_pos_log_enable" type="bool" value="1" />
	<param name="cube_side_length" type="double" value="2000" />
  </node>

  <!-- <group if="$(arg rviz)">
    <node launch-prefix="nice" pkg="rviz" type="rviz" name="rviz" args="-d $(find fast_lio)/rviz_cfg/loam_livox.rviz" />
  </group> -->

</launch>
